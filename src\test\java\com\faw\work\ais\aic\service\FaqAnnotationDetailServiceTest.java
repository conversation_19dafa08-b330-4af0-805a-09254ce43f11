package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.FaqHitLogDetailQueryRequest;
import com.faw.work.ais.aic.model.response.FaqHitLogDetailResponse;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FAQ标注详情服务测试类
 * 用于测试分页查询功能是否正常
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class FaqAnnotationDetailServiceTest {

    @Autowired
    private FaqAnnotationDetailService faqAnnotationDetailService;

    /**
     * 测试分页查询功能
     * 验证total数量是否正确
     */
    @Test
    public void testGetTaskDetailsWithPagination() {
        // 准备测试数据
        FaqHitLogDetailQueryRequest request = new FaqHitLogDetailQueryRequest();
        request.setAnnotationTaskId("1947886381079805954"); // 使用您提到的任务ID
        request.setPageNum(1);
        request.setPageSize(2);
        request.setAnnotationType(null);
        request.setIsLocked(null);

        // 执行查询
        PageInfo<FaqHitLogDetailResponse> result = faqAnnotationDetailService.getTaskDetails(request);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        log.info("分页查询结果 - 总数: {}, 当前页: {}, 每页大小: {}, 实际返回数量: {}", 
                result.getTotal(), result.getPageNum(), result.getPageSize(), result.getList().size());

        // 验证分页信息的一致性
        assertTrue(result.getTotal() >= 0, "总数应该大于等于0");
        assertEquals(1, result.getPageNum(), "当前页码应该是1");
        assertEquals(2, result.getPageSize(), "每页大小应该是2");
        
        // 如果有数据，验证返回的数据量不超过每页大小
        if (result.getTotal() > 0) {
            assertTrue(result.getList().size() <= result.getPageSize(), 
                    "返回的数据量不应超过每页大小");
            
            // 如果总数大于每页大小，第一页应该返回完整的每页大小数量
            if (result.getTotal() > result.getPageSize()) {
                assertEquals(result.getPageSize(), result.getList().size(), 
                        "当总数大于每页大小时，第一页应该返回完整的每页大小数量");
            }
        }

        // 测试第二页
        if (result.getTotal() > 2) {
            request.setPageNum(2);
            PageInfo<FaqHitLogDetailResponse> page2Result = faqAnnotationDetailService.getTaskDetails(request);
            
            assertNotNull(page2Result, "第二页查询结果不应为空");
            assertEquals(result.getTotal(), page2Result.getTotal(), "总数在不同页之间应该保持一致");
            assertEquals(2, page2Result.getPageNum(), "第二页的页码应该是2");
            
            log.info("第二页查询结果 - 总数: {}, 当前页: {}, 实际返回数量: {}", 
                    page2Result.getTotal(), page2Result.getPageNum(), page2Result.getList().size());
        }
    }

    /**
     * 测试带筛选条件的分页查询
     */
    @Test
    public void testGetTaskDetailsWithFilters() {
        // 准备测试数据 - 只查询已锁定的数据
        FaqHitLogDetailQueryRequest request = new FaqHitLogDetailQueryRequest();
        request.setAnnotationTaskId("1947886381079805954");
        request.setPageNum(1);
        request.setPageSize(10);
        request.setIsLocked(true); // 只查询已锁定的

        // 执行查询
        PageInfo<FaqHitLogDetailResponse> result = faqAnnotationDetailService.getTaskDetails(request);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        log.info("筛选查询结果 - 总数: {}, 当前页: {}, 每页大小: {}, 实际返回数量: {}", 
                result.getTotal(), result.getPageNum(), result.getPageSize(), result.getList().size());

        // 验证所有返回的数据都符合筛选条件
        for (FaqHitLogDetailResponse response : result.getList()) {
            assertTrue(response.getIsLocked(), "所有返回的数据都应该是已锁定的");
        }
    }

    /**
     * 测试统计方法
     */
    @Test
    public void testCountMethods() {
        String taskId = "1947886381079805954";
        
        // 测试总数统计
        int totalCount = faqAnnotationDetailService.countByTaskId(taskId);
        log.info("任务总数据量: {}", totalCount);
        assertTrue(totalCount >= 0, "总数应该大于等于0");

        // 测试已标注数量统计
        int annotatedCount = faqAnnotationDetailService.countAnnotatedByTaskId(taskId);
        log.info("已标注数量: {}", annotatedCount);
        assertTrue(annotatedCount >= 0, "已标注数量应该大于等于0");
        assertTrue(annotatedCount <= totalCount, "已标注数量不应该超过总数");
    }
}
