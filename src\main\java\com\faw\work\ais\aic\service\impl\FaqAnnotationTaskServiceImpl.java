package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.mapper.faq.FaqAnnotationTaskMapper;
import com.faw.work.ais.aic.mapper.faq.FaqRobotMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO;
import com.faw.work.ais.aic.model.domain.FaqHitLogPO;
import com.faw.work.ais.aic.model.domain.FaqRobotPO;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskCreateRequest;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskQueryRequest;
import com.faw.work.ais.aic.model.response.FaqAnnotationTaskResponse;
import com.faw.work.ais.aic.service.FaqAnnotationDetailService;
import com.faw.work.ais.aic.service.FaqAnnotationTaskService;
import com.faw.work.ais.aic.service.FaqHitLogService;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * FAQ标注任务Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqAnnotationTaskServiceImpl implements FaqAnnotationTaskService {

    public static final int THIRTY_ONE = 31;
    @Autowired
    private FaqAnnotationTaskMapper faqAnnotationTaskMapper;

    @Autowired
    private FaqHitLogService faqHitLogService;

    @Autowired
    private FaqAnnotationDetailService faqAnnotationDetailService;

    @Autowired
    private FaqRobotMapper faqRobotMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTask(FaqAnnotationTaskPO task) {
        if (ObjectUtil.isNull(task)) {
            throw new IllegalArgumentException("标注任务信息不能为空");
        }
        
        if (StrUtil.isBlank(task.getTaskName())) {
            throw new IllegalArgumentException("任务名称不能为空");
        }

        String robotId = task.getRobotId();
        FaqRobotPO faqRobotPO = faqRobotMapper.selectById(robotId);
        if (faqRobotPO == null) {
            throw new IllegalArgumentException("机器人不存在");
        }
        task.setRobotName(faqRobotPO.getRobotName());


        // 设置默认值
        if (StrUtil.isBlank(task.getStatus())) {
            task.setStatus("processing");
        }
        
        if (ObjectUtil.isNull(task.getTotalCount())) {
            task.setTotalCount(0);
        }
        
        if (ObjectUtil.isNull(task.getAnnotatedCount())) {
            task.setAnnotatedCount(0);
        }
        
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());

        int result = faqAnnotationTaskMapper.insert(task);
        if (result > 0) {
            log.info("成功创建标注任务，任务ID: {}, 任务名称: {}", task.getId(), task.getTaskName());
            return task.getId();
        } else {
            throw new RuntimeException("创建标注任务失败");
        }
    }

    @Override
    public FaqAnnotationTaskPO getTaskById(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        return faqAnnotationTaskMapper.selectById(taskId);
    }

    @Override
    public List<FaqAnnotationTaskPO> getTasksByRobotId(String robotId) {
        if (StrUtil.isBlank(robotId)) {
            throw new IllegalArgumentException("机器人ID不能为空");
        }
        
        return faqAnnotationTaskMapper.findByRobotId(robotId);
    }

    @Override
    public List<FaqAnnotationTaskPO> getTasksByCreatorId(String creatorId) {
        if (StrUtil.isBlank(creatorId)) {
            throw new IllegalArgumentException("创建人ID不能为空");
        }
        
        return faqAnnotationTaskMapper.findByCreatorId(creatorId);
    }

    @Override
    public List<FaqAnnotationTaskPO> getTasksByStatus(String status) {
        if (StrUtil.isBlank(status)) {
            throw new IllegalArgumentException("任务状态不能为空");
        }
        
        return faqAnnotationTaskMapper.findByStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeTask(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        int result = faqAnnotationTaskMapper.updateStatusToCompleted(taskId);
        if (result > 0) {
            log.info("成功完成标注任务，任务ID: {}", taskId);
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAnnotatedCount(String taskId, Integer annotatedCount) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        if (ObjectUtil.isNull(annotatedCount) || annotatedCount < 0) {
            throw new IllegalArgumentException("已标注数量不能为空且不能小于0");
        }
        
        int result = faqAnnotationTaskMapper.updateAnnotatedCount(taskId, annotatedCount);
        if (result > 0) {
            log.info("成功更新标注任务已标注数量，任务ID: {}, 已标注数量: {}", taskId, annotatedCount);
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTask(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        int result = faqAnnotationTaskMapper.deleteById(taskId);
        if (result > 0) {
            log.info("成功删除标注任务，任务ID: {}", taskId);
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAnnotationTask(FaqAnnotationTaskCreateRequest request) {
        if (ObjectUtil.isNull(request)) {
            throw new IllegalArgumentException("创建请求不能为空");
        }

        // 校验时间范围（最大1个月）
        long daysBetween = ChronoUnit.DAYS.between(request.getStartTime(), request.getEndTime());
        if (daysBetween > THIRTY_ONE) {
            throw new IllegalArgumentException("数据抽取时间跨度不能超过1个月");
        }

        // 创建标注任务
        FaqAnnotationTaskPO task = new FaqAnnotationTaskPO()
                .setTaskName(request.getTaskName())
                .setRobotId(request.getRobotId())
                // .setRobotName(request.getRobotName())
                .setDataSource(request.getDataSource())
                .setCallType(request.getCallType())
                .setStartTime(request.getStartTime())
                .setEndTime(request.getEndTime())
                .setStatus("processing")
                .setTotalCount(0)
                .setAnnotatedCount(0)
                .setCreatorId(UserThreadLocalUtil.getCurrentName())
                .setCreatorName(UserThreadLocalUtil.getRealName())
                .setCreatedAt(LocalDateTime.now())
                .setUpdatedAt(LocalDateTime.now());

        String taskId = createTask(task);

        // 抽取FAQ命中日志数据
        List<FaqHitLogPO> hitLogs = faqHitLogService.findHitLogsByCondition(
                request.getRobotId(),
                request.getDataSource(),
                request.getStartTime(),
                request.getEndTime()
        );

        if (hitLogs.size() == 0) {
           throw new RuntimeException("未找到有效FAQ命中日志数据");
        }

        // 创建标注详情记录
        List<FaqAnnotationDetailPO> details = new ArrayList<>();
        for (FaqHitLogPO hitLog : hitLogs) {
            FaqAnnotationDetailPO detail = new FaqAnnotationDetailPO()
                    .setTaskId(taskId)
                    .setHitLogId(hitLog.getId())
                    .setUserQuestion(hitLog.getUserQuestion())
                    .setMatchType(hitLog.getIsHit() != null && hitLog.getIsHit() ? "has_answer" : "no_answer")
                    .setMatchedContent(hitLog.getMatchedAnswer())
                    .setKnowledgeId(hitLog.getKnowledgeId())
                    .setFaqTitle(hitLog.getFaqTitle())
                    .setMatchScore(hitLog.getMatchScore())
                    .setIsLocked(false);
            details.add(detail);
        }

        // 批量插入标注详情
        faqAnnotationDetailService.batchCreateDetails(details);

        // 更新任务的总数据量
        task.setTotalCount(hitLogs.size());
        faqAnnotationTaskMapper.updateById(task);

        log.info("成功创建标注任务，任务ID: {}, 抽取数据量: {}", taskId, hitLogs.size());
        return taskId;
    }

    @Override
    public PageInfo<FaqAnnotationTaskResponse> getAnnotationTaskList(FaqAnnotationTaskQueryRequest request) {
        if (ObjectUtil.isNull(request)) {
            throw new IllegalArgumentException("查询请求不能为空");
        }

        // 使用PageHelper开启分页
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        // 执行查询
        List<FaqAnnotationTaskPO> tasks = faqAnnotationTaskMapper.findTasks(
                request.getTaskName(),
                request.getStatus()
        );

        // 先创建原始数据的PageInfo，确保获取正确的分页信息
        PageInfo<FaqAnnotationTaskPO> originalPageInfo = new PageInfo<>(tasks);

        // 转换为响应对象
        List<FaqAnnotationTaskResponse> responses = tasks.stream()
                .map(task -> {
                    FaqAnnotationTaskResponse response = new FaqAnnotationTaskResponse();
                    BeanUtils.copyProperties(task, response);
                    return response;
                })
                .collect(Collectors.toList());

        // 创建结果PageInfo并复制分页信息
        PageInfo<FaqAnnotationTaskResponse> result = new PageInfo<>(responses);
        result.setTotal(originalPageInfo.getTotal());
        result.setPages(originalPageInfo.getPages());
        result.setPageSize(originalPageInfo.getPageSize());
        result.setPageNum(originalPageInfo.getPageNum());
        result.setSize(originalPageInfo.getSize());
        result.setStartRow(originalPageInfo.getStartRow());
        result.setEndRow(originalPageInfo.getEndRow());
        result.setIsFirstPage(originalPageInfo.isIsFirstPage());
        result.setIsLastPage(originalPageInfo.isIsLastPage());
        result.setHasPreviousPage(originalPageInfo.isHasPreviousPage());
        result.setHasNextPage(originalPageInfo.isHasNextPage());
        result.setNavigatePages(originalPageInfo.getNavigatePages());
        result.setNavigatepageNums(originalPageInfo.getNavigatepageNums());
        result.setNavigateFirstPage(originalPageInfo.getNavigateFirstPage());
        result.setNavigateLastPage(originalPageInfo.getNavigateLastPage());
        result.setPrePage(originalPageInfo.getPrePage());
        result.setNextPage(originalPageInfo.getNextPage());

        return result;
    }



    @Override
    public boolean canDeleteTask(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        FaqAnnotationTaskPO task = faqAnnotationTaskMapper.selectById(taskId);
        if (ObjectUtil.isNull(task)) {
            return false;
        }

        // 只有已完成的任务才能删除
        return "completed".equals(task.getStatus());
    }
}
