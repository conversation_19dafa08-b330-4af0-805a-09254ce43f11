package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息业务类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {
    /**
     * 情绪识别模型
     */
    DMS_EMOTION_PRODUCT("dms_emotion_product", "情绪识别模型"),
    /**
     * 情绪识别模型ab
     */
    DMS_EMOTION_PRODUCT_AB("dms_emotion_product_ab", "情绪识别模型");

    private final String code;
    private final String desc;

}