package com.faw.work.ais.aic.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ai请求
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiRequest {


    /**
     * 模型选择(三种固定为qwen-max，qwen-plus，qwen-turbo)
     */
    private String modelName;

    /**
     * Prompt设置
     */
    private String prompt;

    /**
     * 温度系数 (0-1.99)
     */
    private Double temperature;

    /**
     * 最长回复长度
     */
    private Integer maxResponseLength;

    /**
     * 携带上下文轮数
     */
    private Integer contextRounds;

    /**
     * 用户输入内容
     */
    private String userInput;

}
