package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.aic.mapper.faq.FaqAnnotationDetailMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis-Plus 分页功能测试
 * 用于验证分页插件是否正常工作
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class MybatisPlusPaginationTest {

    @Autowired
    private FaqAnnotationDetailMapper faqAnnotationDetailMapper;

    /**
     * 测试 MyBatis-Plus 分页查询
     */
    @Test
    public void testMybatisPlusPagination() {
        String taskId = "1947886381079805954";
        
        // 创建分页对象
        Page<FaqAnnotationDetailPO> page = new Page<>(1, 2);
        
        // 执行分页查询
        IPage<FaqAnnotationDetailPO> result = faqAnnotationDetailMapper.findByTaskIdPage(
                page, taskId, null, null);
        
        // 验证结果
        assertNotNull(result, "分页结果不应为空");
        log.info("MyBatis-Plus分页查询结果:");
        log.info("总数: {}", result.getTotal());
        log.info("当前页: {}", result.getCurrent());
        log.info("每页大小: {}", result.getSize());
        log.info("总页数: {}", result.getPages());
        log.info("实际返回记录数: {}", result.getRecords().size());
        
        // 基本验证
        assertTrue(result.getTotal() >= 0, "总数应该大于等于0");
        assertEquals(1, result.getCurrent(), "当前页应该是1");
        assertEquals(2, result.getSize(), "每页大小应该是2");
        
        // 如果有数据，验证分页逻辑
        if (result.getTotal() > 0) {
            assertTrue(result.getRecords().size() <= result.getSize(), 
                    "返回的记录数不应超过每页大小");
            
            // 如果总数大于每页大小，验证第一页返回完整数据
            if (result.getTotal() > result.getSize()) {
                assertEquals(result.getSize(), result.getRecords().size(), 
                        "当总数大于每页大小时，第一页应该返回完整的每页大小数量");
            }
            
            // 测试第二页
            if (result.getTotal() > 2) {
                Page<FaqAnnotationDetailPO> page2 = new Page<>(2, 2);
                IPage<FaqAnnotationDetailPO> result2 = faqAnnotationDetailMapper.findByTaskIdPage(
                        page2, taskId, null, null);
                
                assertNotNull(result2, "第二页结果不应为空");
                assertEquals(result.getTotal(), result2.getTotal(), "总数在不同页之间应该保持一致");
                assertEquals(2, result2.getCurrent(), "第二页的页码应该是2");
                
                log.info("第二页查询结果:");
                log.info("总数: {}, 当前页: {}, 实际返回记录数: {}", 
                        result2.getTotal(), result2.getCurrent(), result2.getRecords().size());
            }
        }
        
        // 打印详细信息用于调试
        log.info("详细记录信息:");
        for (int i = 0; i < result.getRecords().size(); i++) {
            FaqAnnotationDetailPO record = result.getRecords().get(i);
            log.info("记录 {}: ID={}, 用户问题={}", i + 1, record.getId(), record.getUserQuestion());
        }
    }

    /**
     * 测试带筛选条件的分页查询
     */
    @Test
    public void testPaginationWithFilters() {
        String taskId = "1947886381079805954";
        
        // 测试只查询已锁定的记录
        Page<FaqAnnotationDetailPO> page = new Page<>(1, 10);
        IPage<FaqAnnotationDetailPO> result = faqAnnotationDetailMapper.findByTaskIdPage(
                page, taskId, null, true);
        
        log.info("筛选查询结果 (只查询已锁定):");
        log.info("总数: {}, 当前页: {}, 每页大小: {}, 实际返回记录数: {}", 
                result.getTotal(), result.getCurrent(), result.getSize(), result.getRecords().size());
        
        // 验证所有返回的记录都是已锁定的
        for (FaqAnnotationDetailPO record : result.getRecords()) {
            assertTrue(record.getIsLocked(), "所有返回的记录都应该是已锁定的");
        }
    }

    /**
     * 测试统计方法
     */
    @Test
    public void testCountMethods() {
        String taskId = "1947886381079805954";
        
        // 测试总数统计
        int totalCount = faqAnnotationDetailMapper.countByTaskId(taskId);
        log.info("任务总数据量: {}", totalCount);
        assertTrue(totalCount >= 0, "总数应该大于等于0");

        // 测试已标注数量统计
        int annotatedCount = faqAnnotationDetailMapper.countAnnotatedByTaskId(taskId);
        log.info("已标注数量: {}", annotatedCount);
        assertTrue(annotatedCount >= 0, "已标注数量应该大于等于0");
        assertTrue(annotatedCount <= totalCount, "已标注数量不应该超过总数");
        
        // 验证分页查询的总数与统计方法的总数是否一致
        Page<FaqAnnotationDetailPO> page = new Page<>(1, 10);
        IPage<FaqAnnotationDetailPO> pageResult = faqAnnotationDetailMapper.findByTaskIdPage(
                page, taskId, null, null);
        
        assertEquals(totalCount, pageResult.getTotal(), 
                "分页查询的总数应该与统计方法的总数一致");
    }
}
