package com.faw.work.ais.controller;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.common.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/redis")
@Tag(name = "缓存", description = "缓存相关接口")
@Slf4j
@RequiredArgsConstructor
public class RedisController {

    private final RedisService redisService;
    @GetMapping("/delete/{key}")
    @Operation(summary = "删除缓存键", description = "删除缓存键")
    public Response<Boolean> delete(@PathVariable("key") @Parameter(description = "缓存键") String key) {
        redisService.delete(key);
        return Response.success(Boolean.TRUE);
    }

    @GetMapping("/get/{key}")
    @Operation(summary = "获取缓存值", description = "获取缓存值")
    public Response<String> get(@PathVariable("key") @Parameter(description = "缓存键") String key) {
        Object res = redisService.get(key);
        return Response.success(JSON.toJSONString(res));
    }

}
